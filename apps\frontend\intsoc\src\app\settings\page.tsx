'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  CardContent,
  Badge,
  Alert,
  AlertDescription,
} from '@telesoft/ui';
import { MultiLineChart, MultiLineChartDataPoint } from '@telesoft/d3';
import {
  useDeploymentsWebSocket,
  useDeploymentFiltersConfig,
} from '../../lib/hooks/useDeployments';
import {
  TabGroup,
  ConnectionStatus,
  StatsCard,
  Toggle,
} from '@telesoft/react-components';
import {
  NumberField,
  SelectField,
  FormSection,
  TextField,
} from '@telesoft/forms';
import {
  transformDeploymentData,
  classNames,
  STYLE_PRESETS,
} from '@telesoft/utils';
import Modal from '../../components/Modal';

export default function SettingsPage() {
  const [deploymentModes, setDeploymentModes] = useState<Record<string, 'training' | 'live'>>({});
  const [activeTab, setActiveTab] = useState<'machinelearning' | 'datasources' | 'alertdestinations'>('datasources');

  const {
    deployments,
    isConnected: isDeploymentsConnected,
    error: deploymentsError,
    lastUpdate,
  } = useDeploymentsWebSocket({
    autoConnect: true,
    fallbackToRest: true,
  });

  const { filtersConfig, refresh: refreshFiltersConfig } =
    useDeploymentFiltersConfig();

  const [mlSettings, setMlSettings] = useState({
    modelAccuracyThreshold: 85,
    trainingDataSize: 10000,
    autoRetrain: true,
    anomalyDetectionSensitivity: 'medium',
    threatScoringModel: 'ensemble',
    enableRealtimeProcessing: true,
    maxModelMemory: 4096,
    gpuAcceleration: false,
  });

  const [dataSourceSettings, setDataSourceSettings] = useState({
    logRetentionDays: 90,
    realTimeIngestion: true,
    compressionEnabled: true,
    encryptionAtRest: true,
    maxIngestionRate: 10000,
    dataValidation: true,
    sourceTimeout: 30,
    batchSize: 1000,
  });

  const [alertDestinationSettings, setAlertDestinationSettings] = useState({
    destinationType: 'iris',
    syslogIp: '',
    syslogPort: '',
    filePath: '',
    irisUrl: '',
    irisToken: '',
    irisCustomerId: '',
  });

  const [saveStatus, setSaveStatus] = useState<
    'idle' | 'saving' | 'saved' | 'error'
  >('idle');

  const [isAddDeploymentModalOpen, setIsAddDeploymentModalOpen] =
    useState(false);
  const [deploymentForm, setDeploymentForm] = useState({
    name: '',
    type: '',
    sourceField: '',
    filterType: '',
    filterValue: '',
    sourceMetric: '',
  });
  const [deploymentSubmitStatus, setDeploymentSubmitStatus] = useState<
    'idle' | 'submitting' | 'success' | 'error'
  >('idle');


  const [isDeleteMode, setIsDeleteMode] = useState(false);
  const [selectedDeployments, setSelectedDeployments] = useState<Set<string>>(
    new Set(),
  );
  const [deleteStatus, setDeleteStatus] = useState<
    'idle' | 'deleting' | 'success' | 'error'
  >('idle');


  const [deploymentCreateStatus, setDeploymentCreateStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');

  // State for managing pending deployments (placeholders)
  const [pendingDeployments, setPendingDeployments] = useState<
    Array<{
      id: string;
      name: string;
      namespace: string;
      timestamp: number;
    }>
  >([]);

  // Reorder mode state
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [draggedNamespace, setDraggedNamespace] = useState<string | null>(null);
  const [dragOverNamespace, setDragOverNamespace] = useState<string | null>(
    null,
  );
  const [customOrder, setCustomOrder] = useState<string[]>([]);

  // Deployment type options
  const deploymentTypeOptions = [
    { value: 'spike', label: 'Anomaly Detection' },
    { value: 'dga', label: 'DGA' },
    { value: 'custom', label: 'Custom' },
  ];
  const normalizedDeployments = React.useMemo(() => {
    return deployments
      .filter((dep: any) => dep && (dep.name || dep.NAME) && (dep.namespace || dep.NAMESPACE))
      .map((dep: any) => {
        const name = dep.name || dep.NAME;
        const namespace = dep.namespace || dep.NAMESPACE;
        const displayName = dep.display_name || `${name}-${namespace}`;

        // Handle different data structures based on deployment type
        let data = dep.data;



        if (!data) {
          // Provide appropriate fallback based on deployment type
          if (name === 'dga') {
            data = {
              dga_found: 0,
              domains_checked: 0,
              inconclusive: 0
            };
          } else {
            // Default structure for anomaly detection (spike) deployments
            data = {
              timeseries: {},
              predictions: { '1min': {} },
              alerts: []
            };
          }
        } else {
          // Data exists, but check if DGA data got transformed incorrectly
          if (name === 'dga' && data.timeseries && !data.dga_found) {
            // Try to find the original DGA data in the raw deployments
            const originalDep = deployments.find((d: any) =>
              (d.name || d.NAME) === name && (d.namespace || d.NAMESPACE) === namespace
            );
            if (originalDep && originalDep.data && originalDep.data.dga_found !== undefined) {
              data = originalDep.data;
            }
          }
        }

        return {
          name,
          namespace,
          display_name: displayName,
          data
        };
      });
  }, [deployments]);

  // Transform deployments data for individual charts
  const chartDataByDeployment = React.useMemo(() => {
    if (normalizedDeployments.length === 0) return {};

    // Filter out DGA deployments since they use metric cards instead of charts
    const chartableDeployments = normalizedDeployments.filter(
      (deployment) => deployment.name !== 'dga'
    );

    const transformedData = transformDeploymentData(chartableDeployments, {
      maxPoints: 50,
    });

    return transformedData;
  }, [normalizedDeployments]);


  // Helper function to get ordered deployments
  const getOrderedDeployments = React.useCallback(() => {
    // Include all deployments (both chart-based and DGA metric-based)
    const allDeploymentKeys = normalizedDeployments.map((d: any) => `${d.name}-${d.namespace}`);

    // Filter to only include deployments that actually exist in our normalized data
    const deploymentKeysWithData = allDeploymentKeys.filter((key: string) => {
      const deployment = normalizedDeployments.find((d: any) => `${d.name}-${d.namespace}` === key);
      // Include deployment if it exists and either has chart data OR is a DGA deployment
      return deployment && (chartDataByDeployment[key] || deployment.name === 'dga');
    });

    if (customOrder.length === 0) return deploymentKeysWithData;

    const orderedDeploymentKeys = customOrder.filter((key: string) =>
      deploymentKeysWithData.includes(key),
    );
    const newDeploymentKeys = deploymentKeysWithData.filter(
      (key: string) => !customOrder.includes(key),
    );

    return [...orderedDeploymentKeys, ...newDeploymentKeys];
  }, [chartDataByDeployment, normalizedDeployments, customOrder]);


  // Load saved order from localStorage on mount
  React.useEffect(() => {
    const savedOrder = localStorage.getItem('deployment-card-order');
    if (savedOrder) {
      try {
        const parsedOrder = JSON.parse(savedOrder);
        if (Array.isArray(parsedOrder)) {
          const validKeys = parsedOrder.filter((key: string) =>
            deployments.some(
              (d: any) => `${d.name}-${d.namespace}` === key
            )
          );
          setCustomOrder(validKeys);
        }
      } catch (error) {
        console.warn('Failed to parse saved deployment order:', error);
      }
    }
  }, [deployments]);



  // Save order to   whenever it changes
  React.useEffect(() => {
    if (customOrder.length > 0) {
      localStorage.setItem(
        'deployment-card-order',
        JSON.stringify(customOrder),
      );
    }
  }, [customOrder]);

  // Update custom order when deployments change (new deployments added)
  React.useEffect(() => {
    if (deployments.length > 0) {
      const currentDeploymentKeys = getOrderedDeployments();
      const existingOrderedKeys = customOrder.filter((key) =>
        currentDeploymentKeys.indexOf(key) !== -1,
      );
      const newKeys = currentDeploymentKeys.filter(
        (key) => customOrder.indexOf(key) === -1,
      );

      if (newKeys.length > 0) {
        setCustomOrder((_prev) => [
          ...existingOrderedKeys,
          ...newKeys,
        ]);
      }
    }
  }, [deployments, getOrderedDeployments, customOrder]);

  // Initialize deployment modes for existing deployments (default to Live mode)
  // and set new deployments to Training mode when they are created
  React.useEffect(() => {
    if (deployments.length > 0) {
      setDeploymentModes((prev) => {
        const updated = { ...prev };
        let hasChanges = false;

        deployments.forEach((deployment) => {
          const namespace = deployment.namespace;
          // If this deployment doesn't have a mode set yet, default to Live mode
          // (this handles existing deployments that should default to Live)
          if (!(namespace in updated)) {
            updated[namespace] = 'live';
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }
  }, [deployments]);

  // Clean up stale pending deployments (older than 30 seconds)
  React.useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      setPendingDeployments((_prev) =>
        _prev.filter((pending) => now - pending.timestamp < 30000),
      );
    }, 5000); // Check every 5 seconds

    return () => clearInterval(cleanupInterval);
  }, []);

  // Remove pending deployments when actual deployments appear with matching names
  React.useEffect(() => {
    if (deployments.length > 0 && pendingDeployments.length > 0) {
      const deploymentNames = deployments.map((d) => d.name.toLowerCase());
      setPendingDeployments((prev) =>
        prev.filter(
          (pending) => !deploymentNames.includes(pending.name.toLowerCase()),
        ),
      );
    }
  }, [deployments, pendingDeployments]);

  // Helper function to extract DGA metrics from deployment data
  const getDGAMetrics = (namespaceDeployments: typeof deployments) => {
    // Find the DGA deployment in this namespace
    const dgaDeployment = namespaceDeployments.find((d) => d.name === 'dga');
    if (!dgaDeployment || !dgaDeployment.data) return null;

    // DGA deployments have a special data structure: {"domains_checked":8431,"dga_found":48,"inconclusive":7}
    const data = dgaDeployment.data;

    // Check if this is DGA-style data structure
    if (
      typeof data === 'object' &&
      'domains_checked' in data &&
      'dga_found' in data &&
      'inconclusive' in data
    ) {
      return {
        domainsChecked: data.domains_checked || 0,
        dgaFound: data.dga_found || 0,
        inconclusive: data.inconclusive || 0,
      };
    }

    return null;
  };

  // Helper function to extract DGA metrics from a single deployment
  const getSingleDGAMetrics = (deployment: any) => {
    if (!deployment || deployment.name !== 'dga' || !deployment.data) {
      return null;
    }

    const data = deployment.data;

    // Check if this is DGA-style data structure
    if (
      typeof data === 'object' &&
      'domains_checked' in data &&
      'dga_found' in data &&
      'inconclusive' in data
    ) {
      return {
        domainsChecked: data.domains_checked || 0,
        dgaFound: data.dga_found || 0,
        inconclusive: data.inconclusive || 0,
      };
    }

    return null;
  };



  const getFilterInfoForDeployment = (
    name: string,
    namespace: string,
  ): { key: string; value: string } => {


    if (!filtersConfig || !Array.isArray(filtersConfig)) {
      return { key: 'namespace', value: namespace };
    }

    const filterItem = filtersConfig.find(
      (item: any) => item.name === name && item.namespace === namespace,
    );


    if (filterItem?.FILTER_KEY && filterItem?.FILTER_VALUE) {
      return {
        key: filterItem.FILTER_KEY,
        value: filterItem.FILTER_VALUE,
      };
    }

    return { key: 'namespace', value: namespace };
  };

  // Helper function to get filter info for all deployments in a namespace
  const getFilterInfoForNamespace = (
    namespaceDeploys: typeof deployments,
  ): string[] => {
    if (namespaceDeploys.length === 0) return ['No deployments'];

    // Get all unique filter info for this namespace
    const filterInfos = namespaceDeploys.map((deploy) => {
      const info = getFilterInfoForDeployment(deploy.name, deploy.namespace);
      return `${info.key}: ${info.value}`;
    });

    // Remove duplicates
    return [...new Set(filterInfos)];
  };

  const handleSaveSettings = async () => {
    setSaveStatus('saving');
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (_error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  // Modal handlers for adding deployment
  const handleOpenAddDeploymentModal = () => {
    setIsAddDeploymentModalOpen(true);
    setDeploymentForm({
      name: '',
      type: '',
      sourceField: '',
      filterType: '',
      filterValue: '',
      sourceMetric: '', // New field for Source Metric
    });
    setDeploymentSubmitStatus('idle');
  };

  const handleCloseAddDeploymentModal = () => {
    setIsAddDeploymentModalOpen(false);
    setDeploymentForm({
      name: '',
      type: '',
      sourceField: '',
      filterType: '',
      filterValue: '',
      sourceMetric: '', // New field for Source Metric
    });
    setDeploymentSubmitStatus('idle');
  };

  const handleDeploymentFormChange = (field: string, value: string) => {
    setDeploymentForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCreateDeployment = async () => {
    if (!deploymentForm.name.trim() || !deploymentForm.type) {
      return; // Form validation would go here
    }

    setDeploymentSubmitStatus('submitting');

    // Add a placeholder deployment immediately
    const placeholderDeployment = {
      id: `pending-${Date.now()}`,
      name: deploymentForm.name.trim(),
      namespace: `pending-${deploymentForm.type}-${Date.now()}`, // Unique temporary namespace
      timestamp: Date.now(),
    };
    setPendingDeployments((prev) => [...prev, placeholderDeployment]);

    // Track the start time to ensure minimum 3 second display
    const startTime = Date.now();
    const minDisplayTime = 3000; // 3 seconds

    try {
      const response = await fetch('/api/v1/machine-learning/deployments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: deploymentForm.name.trim(),
          type: deploymentForm.type,
          sourceField: deploymentForm.sourceField,
          filterType: deploymentForm.filterType,
          filterValue: deploymentForm.filterValue,
          COUNT_TYPE: deploymentForm.sourceMetric || '',
          ...(deploymentForm.type === 'spike' && deploymentForm.sourceMetric
            ? {
              env: {
                consumer: {
                  COUNT_TYPE: deploymentForm.sourceMetric,
                },
              },
            }
            : {}),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Deployment created successfully:', result);

      // Set new deployment to Training mode (as per user preference)
      if (result.namespace) {
        setDeploymentModes((prev) => ({
          ...prev,
          [result.namespace]: 'training',
        }));
      }

      // Close modal immediately and show success on main page
      handleCloseAddDeploymentModal();
      setDeploymentCreateStatus('success');
      setTimeout(() => setDeploymentCreateStatus('idle'), 3000);

      // Refresh the filter config after successful deployment creation
      try {
        await refreshFiltersConfig();
        console.log('Filter config refreshed after deployment creation');

        // Calculate remaining time to ensure minimum display duration
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        // Remove the placeholder after minimum display time
        setTimeout(() => {
          setPendingDeployments((prev) =>
            prev.filter((p) => p.id !== placeholderDeployment.id),
          );
        }, remainingTime);
      } catch (filterError) {
        console.error('Failed to refresh filter config:', filterError);

        // Calculate remaining time to ensure minimum display duration even on error
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        // Still remove the placeholder after minimum display time
        setTimeout(() => {
          setPendingDeployments((prev) =>
            prev.filter((p) => p.id !== placeholderDeployment.id),
          );
        }, remainingTime);
      }
    } catch (error) {
      console.error('Failed to create deployment:', error);
      setDeploymentSubmitStatus('error');
      setTimeout(() => setDeploymentSubmitStatus('idle'), 3000);

      // Calculate remaining time to ensure minimum display duration even on deployment error
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

      // Remove the placeholder deployment after minimum display time
      setTimeout(() => {
        setPendingDeployments((prev) =>
          prev.filter((p) => p.id !== placeholderDeployment.id),
        );
      }, remainingTime);
    }
  };

  // Delete mode handlers
  const handleEnterDeleteMode = () => {
    setIsDeleteMode(true);
    setIsReorderMode(false); // Exit reorder mode when entering delete mode
    setSelectedDeployments(new Set());
    setDeleteStatus('idle');
  };

  const handleExitDeleteMode = () => {
    setIsDeleteMode(false);
    setSelectedDeployments(new Set());
    setDeleteStatus('idle');
  };

  const handleToggleDeploymentSelection = (namespace: string) => {
    setSelectedDeployments((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(namespace)) {
        newSet.delete(namespace);
      } else {
        newSet.add(namespace);
      }
      return newSet;
    });
  };


  const handleDeleteSelectedDeployments = async () => {
    if (selectedDeployments.size === 0) return;

    setDeleteStatus('deleting');

    try {
      // Build deployments array for the delete request
      const deploymentsToDelete = Array.from(selectedDeployments).map(
        (namespace) => {
          // Find the deployment with this namespace to get its type and name
          const deployment = deployments.find((d) => d.namespace === namespace);
          if (!deployment) {
            throw new Error(
              `Could not find deployment data for namespace: ${namespace}`,
            );
          }

          return {
            type: deployment.name as 'spike' | 'dga', // deployment.name maps to type
            namespace: deployment.namespace,
          };
        },
      );

      console.log('Deleting deployments:', deploymentsToDelete);

      // Call the DELETE endpoint
      const response = await fetch('/api/v1/machine-learning/deployments', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deployments: deploymentsToDelete,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`,
        );
      }

      const result = await response.json();
      console.log('Delete result:', result);

      // Check if there were any errors in the response
      if (result.errors && result.errors.length > 0) {
        console.warn('Some deletions failed:', result.errors);
        // Still show success if at least some were deleted
        if (result.deletedCount > 0) {
          setDeleteStatus('success');
        } else {
          setDeleteStatus('error');
          setTimeout(() => setDeleteStatus('idle'), 3000);
          return;
        }
      } else {
        setDeleteStatus('success');
      }

      setTimeout(() => {
        handleExitDeleteMode();
      }, 1500);
    } catch (error) {
      console.error('Failed to delete deployments:', error);
      setDeleteStatus('error');
      setTimeout(() => setDeleteStatus('idle'), 3000);
    }
  };

  // Reorder mode handlers
  const handleEnterReorderMode = () => {
    setIsReorderMode(true);
    setIsDeleteMode(false); // Exit delete mode when entering reorder mode
    setDraggedNamespace(null);
    // If we don't have a custom order yet, initialize it with current order
    if (customOrder.length === 0) {
      const currentOrder = getOrderedDeployments();
      setCustomOrder(currentOrder);
    }
  };

  const handleExitReorderMode = () => {
    setIsReorderMode(false);
    setDraggedNamespace(null);
    setDragOverNamespace(null);
    // Reset cursor when exiting reorder mode
    document.body.style.cursor = '';
  };

  const handleDragStart = (namespace: string) => {
    if (!isReorderMode) return;
    setDraggedNamespace(namespace);
    setDragOverNamespace(null);
    // Add a small delay to prevent immediate visual feedback
    setTimeout(() => {
      document.body.style.cursor = 'grabbing';
    }, 0);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!isReorderMode) return;
    e.preventDefault();
  };

  const handleDragEnter = (namespace: string) => {
    if (!isReorderMode || draggedNamespace === namespace) return;
    setDragOverNamespace(namespace);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!isReorderMode) return;
    // Only clear drag over if we're actually leaving the element

    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverNamespace(null);
    }
  };

  const handleDragEnd = () => {
    if (!isReorderMode) return;
    // Reset cursor and clean up state
    document.body.style.cursor = '';
    setDraggedNamespace(null);
    setDragOverNamespace(null);
  };

  const handleDrop = (e: React.DragEvent, targetNamespace: string) => {
    e.preventDefault();

    // Reset cursor
    document.body.style.cursor = '';

    if (
      !isReorderMode ||
      !draggedNamespace ||
      draggedNamespace === targetNamespace
    ) {
      setDragOverNamespace(null);
      return;
    }

    setCustomOrder((prev) => {
      const newOrder = [...prev];
      const draggedIndex = newOrder.indexOf(draggedNamespace);
      const targetIndex = newOrder.indexOf(targetNamespace);

      if (draggedIndex === -1 || targetIndex === -1) {
        return prev;
      }



      // Remove dragged item and insert at target position
      newOrder.splice(draggedIndex, 1);
      newOrder.splice(targetIndex, 0, draggedNamespace);

      return newOrder;
    });

    setDraggedNamespace(null);
    setDragOverNamespace(null);
  };

  const [editDeploymentNamespace, setEditDeploymentNamespace] = useState<string | null>(null);

  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>
        {/* Save Status Alert */}
        {saveStatus !== 'idle' && (
          <div className="mb-6">
            {saveStatus === 'saving' && (
              <Alert variant="info">
                <AlertDescription>Saving settings...</AlertDescription>
              </Alert>
            )}
            {saveStatus === 'saved' && (
              <Alert variant="success">
                <AlertDescription>
                  Settings saved successfully!
                </AlertDescription>
              </Alert>
            )}
            {saveStatus === 'error' && (
              <Alert variant="danger">
                <AlertDescription>
                  Failed to save settings. Please try again.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Main Settings Card */}
        <Card className="shadow-lg">
          <CardHeader>
            {/* Tab Navigation */}
            <TabGroup
              tabs={[
                { id: 'datasources', label: 'Data Sources' },
                { id: 'machinelearning', label: 'Machine Learning' },
                { id: 'alertdestinations', label: 'Alert Destinations' },
              ]}
              activeTab={activeTab}
              onTabChange={(tabId) => {
                // Type assertion to ensure tabId matches our state type
                setActiveTab(tabId as 'datasources' | 'machinelearning' | 'alertdestinations');
              }}
            />
          </CardHeader>



          <CardContent className="p-6">
            {/* Machine Learning Settings (Charts) */}
            {activeTab === 'machinelearning' && (
              <div className="space-y-4">
                {/* Error Display */}
                {deploymentsError && (
                  <Alert variant="danger" className="mb-4">
                    <AlertDescription>
                      <strong>Deployment Data Error:</strong> {deploymentsError}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Deployment Summary */}
                <div>

                  <div className="flex justify-between items-center">
                    <div className="w-1/3">
                      <h3 className="text-lg font-semibold text-text-primary">
                        Overview
                      </h3>
                    </div>
                    <div className="w-2/3 flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-text-primary">
                        Deployments
                      </h3>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        {isDeleteMode ? (
                          // Delete mode controls
                          <>
                            <span className="text-sm text-text-secondary mr-2">
                              {selectedDeployments.size} selected
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleExitDeleteMode}
                              disabled={deleteStatus === 'deleting'}
                            >
                              Cancel
                            </Button>
                            <Button
                              variant="danger"
                              size="sm"
                              onClick={handleDeleteSelectedDeployments}
                              loading={deleteStatus === 'deleting'}
                              disabled={
                                deleteStatus === 'deleting' ||
                                selectedDeployments.size === 0
                              }
                            >
                              {deleteStatus === 'deleting'
                                ? 'Deleting...'
                                : `Delete ${selectedDeployments.size > 0 ? `(${selectedDeployments.size})` : ''}`}
                            </Button>
                          </>
                        ) : isReorderMode ? (
                          // Reorder mode controls
                          <>
                            <span className="text-sm text-text-secondary mr-2">
                              Drag cards to reorder
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleExitReorderMode}
                            >
                              Done
                            </Button>
                          </>
                        ) : (
                          // Normal mode controls
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleEnterReorderMode}
                              disabled={deployments.length === 0}
                            >
                              <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M4 6h16M4 10h16M4 14h16M4 18h16"
                                />
                              </svg>
                              Reorder
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleEnterDeleteMode}
                              disabled={deployments.length === 0}
                            >
                              <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9m1 1v1h4V4z"
                                />
                              </svg>
                              Delete
                            </Button>
                            <Button
                              variant="modern"
                              size="sm"
                              onClick={handleOpenAddDeploymentModal}
                            >
                              <span className="text-base">+</span>
                              Add Deployment
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Delete mode status messages */}
                  {isDeleteMode && (
                    <div className="mt-3">
                      {deleteStatus === 'success' && (
                        <Alert variant="success">
                          <AlertDescription>
                            Selected deployments deleted successfully!
                          </AlertDescription>
                        </Alert>
                      )}
                      {deleteStatus === 'error' && (
                        <Alert variant="danger">
                          <AlertDescription>
                            Failed to delete deployments. Please try again.
                          </AlertDescription>
                        </Alert>
                      )}
                      {deleteStatus === 'idle' && (
                        <Alert variant="info">
                          <AlertDescription>
                            Click on deployment cards to select them for
                            deletion.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}

                  {/* Reorder mode status messages */}
                  {isReorderMode && (
                    <div className="mt-3">
                      <Alert variant="info">
                        <AlertDescription>
                          Drag and drop cards to reorder them. Changes are saved
                          automatically.
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {/* Deployment creation success message */}
                  {deploymentCreateStatus === 'success' && (
                    <div className="mt-3">
                      <Alert variant="success">
                        <AlertDescription>
                          Deployment created successfully!
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {deploymentCreateStatus === 'error' && (
                    <div className="mt-3">
                      <Alert variant="danger">
                        <AlertDescription>
                          Failed to create deployment. Please try again.
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                </div>

                {/* Main Content Layout - Left sidebar and Right chart */}
                <div className="flex gap-6 min-h-[600px]">
                  {/* Left Sidebar - Summary and Controls (1/3 width) */}
                  <div className="w-1/3 space-y-6">
                    {/* Summary Cards */}
                    <div className="space-y-4">
                      <Card>
                        <CardContent className="p-4">
                          <ConnectionStatus
                            isConnected={isDeploymentsConnected}
                            lastUpdate={lastUpdate}
                            label="Connection Status"
                            showLastUpdate={true}
                          />
                        </CardContent>
                      </Card>
                      {deployments.length > 0 && (
                        <>
                          <StatsCard
                            title="Active Deployments"
                            value={deployments.length}
                            variant="info"
                          />
                          <StatsCard
                            title="Total Data Points"
                            value={deployments.reduce(
                              (sum, d) => sum + (d.data && typeof d.data === 'object' ? Object.keys(d.data).length : 0),
                              0,
                            )}
                            variant="default"
                          />
                          <StatsCard
                            title="Active Charts"
                            value={Object.keys(chartDataByDeployment).length}
                            variant="success"
                          />
                        </>
                      )}
                    </div>
                  </div>

                  {/* Right Chart Area (2/3 width) */}
                  <div className="w-2/3">
                    {Object.keys(chartDataByDeployment).length > 0 ||
                      deployments.some((d) => d.name === 'dga') ? (
                      <div className="space-y-4">
                        {/* Show placeholder cards if any exist - they hide all other deployments */}
                        {pendingDeployments.length > 0
                          ? pendingDeployments.map((pendingDeployment) => (
                            <Card
                              key={pendingDeployment.id}
                              className="relative opacity-75 border-dashed border-2 border-blue-300"
                            >
                              <CardHeader className="pb-3">
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-3">
                                    <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                    <div>
                                      <h3 className="text-base font-semibold text-text-primary">
                                        {pendingDeployment.name}
                                      </h3>
                                      <div className="text-xs text-text-secondary">
                                        <div>
                                          Status: Creating deployment...
                                        </div>
                                        <div className="ml-2">
                                          Fetching filter configuration...
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="text-blue-500">
                                    <svg
                                      className="w-5 h-5"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent className="pt-0">
                                <div className="w-full relative py-8 flex items-center justify-center">
                                  <div className="text-center">
                                    <div className="animate-pulse">
                                      <div className="w-32 h-4 bg-surface-secondary rounded mb-2 mx-auto"></div>

                                      <div className="w-24 h-3 bg-surface-secondary/70 rounded mx-auto"></div>
                                    </div>
                                    <div className="text-sm text-text-secondary mt-4">
                                      Initializing deployment...
                                    </div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))
                          : /* Render deployments in order - only when no placeholders exist */
                          getOrderedDeployments()

                            .map((deploymentKey) => {
                              // Get chart data if available
                              const chartData =
                                chartDataByDeployment[deploymentKey];

                              // Find the specific deployment for this key
                              const deployment = normalizedDeployments.find(
                                (d: any) => `${d.name}-${d.namespace}` === deploymentKey,
                              );

                              if (!deployment) return null;

                              const displayName = deployment.display_name || deployment.name;
                              const isSelected =
                                selectedDeployments.has(deploymentKey);

                              // Check if this is a DGA deployment
                              const dgaMetrics = getSingleDGAMetrics(deployment);
                              const isDGA = dgaMetrics !== null;

                              // Check if this card is being dragged over for reordering
                              const isDraggedOver =
                                isReorderMode &&
                                dragOverNamespace === deploymentKey;
                              const isDraggedItem =
                                isReorderMode &&
                                draggedNamespace === deploymentKey;

                              return (
                                <React.Fragment key={deploymentKey}>
                                  {/* Drop zone indicator - show above the hovered card */}
                                  {isReorderMode &&
                                    isDraggedOver &&
                                    draggedNamespace &&
                                    draggedNamespace !== deploymentKey && (
                                      <div className="relative">
                                        <div className="h-1 bg-blue-400 rounded-full mx-4 shadow-lg animate-pulse">
                                          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full shadow-lg">
                                            Drop here
                                          </div>
                                        </div>
                                      </div>
                                    )}

                                  <Card
                                    draggable={isReorderMode}
                                    onDragStart={() =>
                                      isReorderMode &&
                                      handleDragStart(deploymentKey)
                                    }
                                    onDragEnd={handleDragEnd}
                                    onDragOver={handleDragOver}
                                    onDragEnter={() =>
                                      isReorderMode &&
                                      handleDragEnter(deploymentKey)
                                    }
                                    onDragLeave={handleDragLeave}
                                    onDrop={(e) =>
                                      isReorderMode &&
                                      handleDrop(e, deploymentKey)
                                    }
                                    className={classNames(
                                      'relative transition-all duration-200',
                                      isDeleteMode &&
                                      'cursor-pointer hover:shadow-md',
                                      isReorderMode &&
                                      !isDraggedItem &&
                                      'cursor-move hover:shadow-lg',
                                      isReorderMode &&
                                      !isDraggedItem &&
                                      'border-dashed border-2 border-border-secondary',

                                      isReorderMode &&
                                      isDraggedItem &&
                                      'opacity-50 scale-95 rotate-1 shadow-2xl z-50 cursor-grabbing',
                                      isReorderMode &&
                                      isDraggedOver &&
                                      !isDraggedItem &&
                                      'border-accent-primary bg-accent-primary/10 shadow-lg scale-[1.02]',
                                      isSelected &&
                                      'ring-2 ring-red-500/40 bg-red-500/10',
                                    )}
                                    onClick={
                                      isDeleteMode
                                        ? () =>
                                          handleToggleDeploymentSelection(
                                            deploymentKey,
                                          )
                                        : undefined
                                    }
                                  >
                                    <CardHeader className="pb-3">
                                      <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-3">
                                          {/* Drag handle in reorder mode */}
                                          {isReorderMode && (
                                            <div className="flex items-center text-text-subtle cursor-move">
                                              <svg
                                                className="w-5 h-5"
                                                fill="currentColor"
                                                viewBox="0 0 20 20"
                                              >
                                                <path d="M7 2a1 1 0 00-1 1v2a1 1 0 102 0V4a1 1 0 00-1-1zM7 8a1 1 0 00-1 1v2a1 1 0 102 0V9a1 1 0 00-1-1zM7 14a1 1 0 00-1 1v2a1 1 0 102 0v-1a1 1 0 00-1-1zM13 2a1 1 0 00-1 1v2a1 1 0 102 0V4a1 1 0 00-1-1zM13 8a1 1 0 00-1 1v2a1 1 0 102 0V9a1 1 0 00-1-1zM13 14a1 1 0 00-1 1v2a1 1 0 102 0v-1a1 1 0 00-1-1z" />
                                              </svg>
                                            </div>
                                          )}

                                          {/* Selection checkbox in delete mode */}
                                          {isDeleteMode && (
                                            <div className="flex items-center">
                                              <input
                                                type="checkbox"
                                                checked={isSelected}
                                                onChange={() =>
                                                  handleToggleDeploymentSelection(
                                                    deploymentKey,
                                                  )
                                                }
                                                className="w-4 h-4 text-red-600 border-border-primary rounded focus:ring-red-500"
                                                onClick={(e) =>
                                                  e.stopPropagation()
                                                }
                                              />
                                            </div>
                                          )}
                                          <div>
                                            <h3 className="text-base font-semibold text-text-primary">
                                              {displayName}
                                            </h3>
                                            <div className="text-xs text-text-secondary">
                                              <div>Filter:</div>
                                              {getFilterInfoForNamespace(
                                                [deployment],
                                              ).map((filterInfo, idx) => (
                                                <div
                                                  key={idx}
                                                  className="ml-2"
                                                >
                                                  {filterInfo}
                                                </div>
                                              ))}
                                            </div>
                                          </div>
                                        </div>

                                        {/* Top right toggle button with state and status */}
                                        <div className="absolute top-3 right-3 z-20 flex items-center gap-2">
                                          <Toggle
                                            isActive={deploymentModes[deploymentKey] === 'live'}
                                            onChange={() => {
                                              setDeploymentModes((prev) => ({
                                                ...prev,
                                                [deploymentKey]: prev[deploymentKey] === 'live' ? 'training' : 'live',
                                              }));
                                            }}
                                            label=""
                                            description=""
                                          />
                                          {/* Edit button */}
                                          <button
                                            type="button"
                                            className="px-2 py-1 border border-border-primary rounded text-xs font-semibold text-text-primary bg-transparent hover:bg-accent-primary/10 hover:text-accent-primary focus:outline-none focus:ring-2 focus:ring-accent-primary transition-colors"
                                            title="Edit deployment"
                                            onClick={() => setEditDeploymentNamespace(deploymentKey)}
                                          >
                                            EDIT
                                          </button>
                                          <span
                                            className={
                                              deploymentModes[deploymentKey] === 'live'
                                                ? 'bg-green-500/90 text-white text-xs font-semibold ml-1 px-3 py-1 rounded-full transition-colors duration-200'
                                                : 'bg-orange-400/90 text-white text-xs font-semibold ml-1 px-3 py-1 rounded-full transition-colors duration-200'
                                            }
                                            style={{
                                              display: 'inline-block',
                                              width: 120,
                                              minWidth: 120,
                                              maxWidth: 120,
                                              textAlign: 'center',
                                              whiteSpace: 'nowrap',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                            }}
                                          >
                                            Status: {deploymentModes[deploymentKey] === 'live' ? 'Live' : 'Training'}
                                          </span>
                                        </div>
                                      </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                      <div className="w-full relative">
                                        {isDGA ? (
                                          // DGA Metrics Display
                                          <div className="grid grid-cols-3 gap-4 py-6">
                                            <div className="text-center">
                                              <div className="flex items-center justify-center mb-2">
                                                <div className="w-4 h-8 bg-blue-500 rounded-sm mr-1"></div>
                                                <div>
                                                  <div className="text-sm font-medium text-text-secondary">
                                                    All Domains Checked
                                                  </div>
                                                  <div className="text-2xl font-bold text-text-primary">
                                                    {dgaMetrics.domainsChecked.toLocaleString()}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                            <div className="text-center">
                                              <div className="flex items-center justify-center mb-2">
                                                <div className="w-4 h-8 bg-red-500 rounded-sm mr-1"></div>
                                                <div>
                                                  <div className="text-sm font-medium text-text-secondary">
                                                    DGA Domains
                                                  </div>
                                                  <div className="text-2xl font-bold text-text-primary">
                                                    {dgaMetrics.dgaFound}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                            <div className="text-center">
                                              <div className="flex items-center justify-center mb-2">
                                                <div className="w-4 h-8 bg-yellow-500 rounded-sm mr-1"></div>
                                                <div>
                                                  <div className="text-sm font-medium text-text-secondary">
                                                    Inconclusive
                                                  </div>
                                                  <div className="text-2xl font-bold text-text-primary">
                                                    {dgaMetrics.inconclusive}
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>

                                        ) : chartData ? (
                                          <>
                                            {deployment.data && chartData && (
                                              <div className="space-y-6">
                                                {/* Charts Section */}
                                                <div className="w-full">
                                                  <Card>
                                                    <CardContent>
                                                      <div className="relative w-full">
                                                        {/* Base MultiLineChart for lines */}
                                                        <MultiLineChart
                                                          datasets={chartData.map((dataset: any) => ({
                                                            ...dataset,
                                                            lineColor: dataset.color,
                                                            data: dataset.data.map((point: MultiLineChartDataPoint) => ({
                                                              ...point,
                                                              x: typeof point.x === 'string' ? new Date(point.x) : point.x
                                                            })),
                                                            // Fix alert point size to match other points
                                                            pointRadius: dataset.label.includes('(Alerts)') ? 3 : dataset.pointRadius
                                                          }))}
                                                          width={800}
                                                          height={200}
                                                          margin={{
                                                            top: 35,
                                                            right: 120,
                                                            bottom: 40,
                                                            left: 60
                                                          }}
                                                          strokeWidth={2}
                                                          showLegend={true}
                                                          legendLocation="top"
                                                          legendAlign="horizontal"
                                                          className="w-full"
                                                        />
                                                      </div>
                                                    </CardContent>
                                                  </Card>
                                                </div>
                                              </div>
                                            )}
                                          </>
                                        ) : null}

                                        {/* Selection overlay in delete mode */}
                                        {isDeleteMode && isSelected && (
                                          <div className="absolute top-3 right-3 z-10">
                                            <div
                                              className={classNames(
                                                'backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border transition-all duration-300',
                                                'bg-surface-primary/70 border-border-primary/30',
                                                'ring-1 ring-red-500/30 bg-red-500/20 border-red-500/60',
                                              )}
                                            >
                                              <span
                                                className={classNames(
                                                  'text-[10px] font-medium tracking-wider transition-colors duration-200 uppercase',
                                                  'text-red-600',
                                                )}
                                              >
                                                Selected
                                              </span>
                                            </div>
                                          </div>
                                        )}

                                        {/* Reorder overlay */}
                                        {isReorderMode && !isDraggedItem && (
                                          <div className="absolute top-3 right-3 z-10">
                                            <div
                                              className={classNames(
                                                'backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border transition-all duration-300',
                                                isDraggedOver
                                                  ? 'bg-accent-primary/30 border-accent-primary/70'
                                                  : 'bg-accent-primary/20 border-accent-primary/60',
                                              )}
                                            >
                                              <span
                                                className={classNames(
                                                  'text-[10px] font-medium tracking-wider transition-colors duration-200 uppercase',
                                                  isDraggedOver
                                                    ? 'text-accent-primary'
                                                    : 'text-accent-primary/80',
                                                )}
                                              >
                                                {isDraggedOver
                                                  ? 'Drop here'
                                                  : 'Drag to reorder'}
                                              </span>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </CardContent>
                                  </Card>
                                </React.Fragment>
                              );
                            })
                            .filter(Boolean)}
                      </div>
                    ) : (
                      <Card className="h-full">
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <div>
                              <h3 className="text-lg font-semibold text-text-primary">
                                Deployment Metrics Over Time
                              </h3>
                              <p className="text-sm text-text-secondary">
                                No deployment data available
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center justify-center min-h-[400px]">
                            <div className="text-center">
                              <div className="mb-6">
                                <svg
                                  className="w-16 h-16 mx-auto text-text-subtle/40"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={1.5}
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                  />
                                </svg>
                              </div>
                              <h4 className="text-lg font-medium text-text-primary mb-2">
                                {deployments.length === 0
                                  ? 'No Deployment Data'
                                  : 'Loading Chart...'}
                              </h4>
                              <p className="text-text-secondary mb-4">
                                {deployments.length === 0
                                  ? 'Connect to the ML service to view deployment metrics'
                                  : 'Preparing chart data...'}
                              </p>
                              {deploymentsError && (
                                <p className="text-sm text-red-500">
                                  Error: {deploymentsError}
                                </p>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Data Sources Settings */}
            {activeTab === 'datasources' && (
              <div className="space-y-6">
                {/* Header with Add button */}
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl font-semibold text-text-primary mb-2">
                      Data Sources Configuration
                    </h2>
                    <p className="text-text-secondary">
                      Configure data ingestion, storage, and processing parameters.
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Add
                  </Button>
                </div>

                {/* Network Probe Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    Network Probe
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      {/* Left side - Combined Stats and Status Box */}
                      <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-4 text-center min-w-[160px]">
                        {/* Speedometer */}
                        <div className="relative flex items-center justify-center mb-3">
                          {/* Circular Progress Background */}
                          <svg className="w-28 h-28 transform -rotate-90" viewBox="0 0 80 80">
                            {/* Background circle */}
                            <circle
                              cx="40"
                              cy="40"
                              r="32"
                              stroke="currentColor"
                              strokeWidth="6"
                              fill="none"
                              className="text-border-primary/30"
                            />
                            {/* Progress circle (0% for 0.0 bps) */}
                            <circle
                              cx="40"
                              cy="40"
                              r="32"
                              stroke="currentColor"
                              strokeWidth="6"
                              fill="none"
                              strokeDasharray={`${0 * 2.01} 201`}
                              className="text-blue-500 transition-all duration-300"
                              strokeLinecap="round"
                            />
                          </svg>
                          {/* Center text */}
                          <div className="absolute inset-0 flex flex-col items-center justify-center">
                            <div className="text-sm text-text-secondary">bps</div>
                            <div className="text-2xl font-bold text-text-primary">0.0</div>
                          </div>
                        </div>

                        {/* MAC 4 */}
                        <div className="text-xs text-text-secondary mb-3">MAC 4</div>

                        {/* Status indicators */}
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div className="text-sm text-green-600">Up 8s</div>
                          </div>
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            <div className="text-sm text-text-secondary">Down 0s</div>
                          </div>
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <div className="text-sm text-text-secondary">Errors 0</div>
                          </div>
                        </div>
                      </div>

                      {/* Center - Device Image */}
                      <div className="flex flex-col items-center justify-center mt-6">
                        <img
                          src="/images/MPAC6650.png"
                          alt="Telesoft MPAC 6650"
                          className="w-71 h-36 object-contain mb-3"
                        />
                        <div className="text-sm font-medium text-text-primary text-center">
                          Telesoft MPAC 6650
                        </div>
                      </div>

                      {/* Right side - Metrics and Actions */}
                      <div className="flex flex-col items-end space-y-4">
                        <div className="flex flex-col space-y-3">
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                            <div className="text-sm text-text-secondary">Active Flows</div>
                            <div className="text-base font-semibold text-text-primary">100,000</div>
                          </div>
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                            <div className="text-sm text-text-secondary">Flows / s</div>
                            <div className="text-base font-semibold text-text-primary">100,000</div>
                          </div>
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                            <div className="text-sm text-text-secondary">Expected Flows / s</div>
                            <div className="text-base font-semibold text-text-primary">100,000</div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Syslog Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    Syslog
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="text-lg font-medium text-text-primary">Messages / S</div>
                        <div className="text-xl font-bold text-text-primary">100,000</div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                          <div className="text-sm text-text-secondary">Listener Port</div>
                          <div className="text-base font-semibold text-text-primary">514</div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* IPFIX Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    IPFIX
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="text-lg font-medium text-text-primary">Flows / S</div>
                        <div className="text-xl font-bold text-text-primary">100,000</div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-6 py-3 text-right min-w-[140px]">
                          <div className="text-sm text-text-secondary">Listener Port</div>
                          <div className="text-base font-semibold text-text-primary">4739</div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Host Sensors Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-text-primary">
                    Host Sensors
                  </h3>

                  <Card className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="text-lg font-medium text-text-primary">Active Sensors</div>
                        <div className="text-xl font-bold text-text-primary">230</div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        <div className="flex flex-col space-y-3">
                          {/* Windows Box */}
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-5 py-3 flex items-center space-x-3 min-w-[140px]">
                            <img
                              src="/images/Windows.png"
                              alt="Windows"
                              className="w-6 h-6 object-contain"
                            />
                            <div>
                              <div className="text-sm text-text-secondary">Windows</div>
                              <div className="text-base font-semibold text-text-primary">200</div>
                            </div>
                          </div>

                          {/* Linux Box */}
                          <div className="bg-surface-secondary/50 border border-border-primary/20 rounded-lg px-5 py-3 flex items-center space-x-3 min-w-[140px]">
                            <img
                              src="/images/Linux.png"
                              alt="Linux"
                              className="w-6 h-6 object-contain"
                            />
                            <div>
                              <div className="text-sm text-text-secondary">Linux</div>
                              <div className="text-base font-semibold text-text-primary">30</div>
                            </div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            )}
            {/* Alert Destinations Settings */}
            {activeTab === 'alertdestinations' && (
              <div className="space-y-6">


                {/* Alert Destinations Configuration Form */}
                <Card className="p-6">
                  <div className="space-y-6">
                    {/* Destination Type Dropdown */}
                    <FormSection title="Destination Configuration">
                      <SelectField
                        label="Destination Type"
                        value={alertDestinationSettings.destinationType}
                        onChange={(value) =>
                          setAlertDestinationSettings((prev) => ({
                            ...prev,
                            destinationType: value,
                          }))
                        }
                        options={[
                          { value: 'iris', label: 'Iris' },
                          { value: 'syslog', label: 'Syslog' },
                          { value: 'json', label: 'JSON' },
                          { value: 'csv', label: 'CSV' },
                        ]}
                        placeholder="Select destination type"
                      />
                    </FormSection>

                    {/* Conditional Fields Based on Destination Type */}
                    {alertDestinationSettings.destinationType === 'syslog' && (
                      <FormSection title="Syslog Configuration">
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <label className="text-sm font-medium text-text-primary whitespace-nowrap">
                              IP:
                            </label>
                            <input
                              type="text"
                              value={alertDestinationSettings.syslogIp}
                              onChange={(e) =>
                                setAlertDestinationSettings((prev) => ({
                                  ...prev,
                                  syslogIp: e.target.value,
                                }))
                              }
                              placeholder="Enter IP address"
                              className="px-3 py-2 border border-border-primary rounded-md focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent"
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <label className="text-sm font-medium text-text-primary whitespace-nowrap">
                              Port:
                            </label>
                            <input
                              type="text"
                              value={alertDestinationSettings.syslogPort}
                              onChange={(e) =>
                                setAlertDestinationSettings((prev) => ({
                                  ...prev,
                                  syslogPort: e.target.value,
                                }))
                              }
                              placeholder="Enter port number"
                              className="px-3 py-2 border border-border-primary rounded-md focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent w-32"
                            />
                          </div>
                        </div>
                      </FormSection>
                    )}

                    {(alertDestinationSettings.destinationType === 'json' ||
                      alertDestinationSettings.destinationType === 'csv') && (
                        <FormSection title="File Configuration">
                          <div className="flex items-center gap-2">
                            <label className="text-sm font-medium text-text-primary whitespace-nowrap">
                              Path:
                            </label>
                            <input
                              type="text"
                              value={alertDestinationSettings.filePath}
                              onChange={(e) =>
                                setAlertDestinationSettings((prev) => ({
                                  ...prev,
                                  filePath: e.target.value,
                                }))
                              }
                              placeholder="Enter file path"
                              className="flex-1 px-3 py-2 border border-border-primary rounded-md focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent"
                            />
                          </div>
                        </FormSection>
                      )}
                    {alertDestinationSettings.destinationType === 'iris' && (
                      <FormSection title="Iris Configuration">
                        <div className="space-y-4">
                          <TextField
                            label="Iris URL"
                            value={alertDestinationSettings.irisUrl}
                            onChange={(value) =>
                              setAlertDestinationSettings((prev) => ({
                                ...prev,
                                irisUrl: value,
                              }))
                            }
                            placeholder="Enter Iris URL"
                            className="w-full"
                          />
                          <TextField
                            label="Iris Token"
                            value={alertDestinationSettings.irisToken}
                            onChange={(value) =>
                              setAlertDestinationSettings((prev) => ({
                                ...prev,
                                irisToken: value,
                              }))
                            }
                            placeholder="Enter API token"
                            className="w-full"
                          />
                          <div className="w-1/2">
                            <TextField
                              label="Iris Customer ID"
                              value={alertDestinationSettings.irisCustomerId}
                              onChange={(value) =>
                                setAlertDestinationSettings((prev) => ({
                                  ...prev,
                                  irisCustomerId: value,
                                }))
                              }
                              placeholder="Enter customer ID"
                            />
                          </div>
                        </div>
                      </FormSection>
                    )}
                  </div>
                </Card>
              </div>
            )}
          </CardContent>

          {/* Action Buttons - Only show on Model and Data Sources tabs */}
          {activeTab !== 'machinelearning' && (
            <div className="flex justify-end gap-4 p-6 border-t border-border-primary/20">
              <Button
                variant="outline"
                onClick={() => {
                  if (activeTab === 'datasources') {
                    setDataSourceSettings({
                      logRetentionDays: 90,
                      realTimeIngestion: true,
                      compressionEnabled: true,
                      encryptionAtRest: true,
                      maxIngestionRate: 10000,
                      dataValidation: true,
                      sourceTimeout: 30,
                      batchSize: 1000,
                    });
                  }
                }}
              >
                Reset to Defaults
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveSettings}
                loading={saveStatus === 'saving'}
                disabled={saveStatus === 'saving'}
              >
                {saveStatus === 'saving' ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          )}
        </Card>
      </div>

      {/* Add Deployment Modal */}
      {isAddDeploymentModalOpen && (
        <Modal
          isOpen={isAddDeploymentModalOpen}
          onClose={handleCloseAddDeploymentModal}
          type="Add New Deployment"
        >
          <div className="p-6 space-y-6">
            <div>
              <p className="text-text-secondary text-sm">
                Create a new machine learning deployment model.
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-4">
              <TextField
                label="Name"
                value={deploymentForm.name}
                onChange={(value) => handleDeploymentFormChange('name', value)}
                placeholder="Enter deployment name"
                error={
                  !deploymentForm.name.trim() &&
                    deploymentSubmitStatus === 'error'
                    ? 'Name is required'
                    : undefined
                }
              />

              <SelectField
                label="Type"
                value={deploymentForm.type}
                onChange={(value) => handleDeploymentFormChange('type', value)}
                options={deploymentTypeOptions}
                placeholder="Select deployment type"
                error={
                  !deploymentForm.type && deploymentSubmitStatus === 'error'
                    ? 'Type is required'
                    : undefined
                }
              />
            </div>

            {/* Model Configuration Panel */}
            {deploymentForm.type === 'spike' && (
              <div className="mt-2">
                <div className="mb-4">
                  <span className="block text-lg font-handwritten mb-2">Model Configuration</span>
                  <div className="flex flex-col gap-4 w-full">
                    <SelectField
                      label="Source Field:"
                      value={deploymentForm.sourceField || ''}
                      onChange={(value) => handleDeploymentFormChange('sourceField', value)}
                      options={[
                        { value: 'destinationTransportPort', label: 'ipfix:destinationTransportPort' },
                        { value: 'sourceTransportPort', label: 'ipfix:sourceTransportPort' },
                        { value: 'destinationCountryAlpha2', label: 'ipfix:destinationCountryAlpha2' },
                        { value: 'sourceCountryAlpha2', label: 'ipfix:sourceCountryAlpha2' },
                        { value: 'destinationServiceName', label: 'ipfix:destinationServiceName' },
                        { value: 'sourceServiceName', label: 'ipfix:sourceServiceName' },
                        { value: 'dnsType', label: 'ipfix:dnsType' },
                        { value: 'dnsResponseCode', label: 'ipfix:dnsResponseCode' },
                        { value: 'ipProtocolName', label: 'ipfix:ipProtocolName' },
                        { value: 'source', label: 'ipfix:source' },
                        { value: 'tcpControlBits', label: 'ipfix:tcpControlBits' },
                        { value: 'tcpControlFlags', label: 'ipfix:tcpControlFlags' },
                      ]}
                      placeholder="Select source field"
                      className="min-w-[220px]"
                    />
                    <SelectField
                      label="Source Metric:"
                      value={deploymentForm.sourceMetric || ''}
                      onChange={(value) => handleDeploymentFormChange('sourceMetric', value)}
                      options={[
                        { value: 'flow', label: 'Flows' },
                        { value: 'packet', label: 'Packets' },
                        { value: 'octet', label: 'Bytes' },
                      ]}
                      placeholder="Select source metric"
                      className="min-w-[160px]"
                    />
                    <SelectField
                      label="Filter Type:"
                      value={deploymentForm.filterType || ''}
                      onChange={(value) => handleDeploymentFormChange('filterType', value)}
                      options={[
                        { value: 'range', label: 'range' },
                        { value: 'equal', label: 'equal' },
                      ]}
                      placeholder="Select filter type"
                      className="min-w-[160px]"
                    />
                    <TextField
                      label="Filter Value:"
                      value={deploymentForm.filterValue || ''}
                      onChange={(value) => handleDeploymentFormChange('filterValue', value)}
                      placeholder="Enter filter value"
                      className="w-48"
                    />
                    <Button
                      variant="outline"
                      type="button"
                      className="w-fit"
                    >
                      Advanced
                    </Button>
                  </div>
                </div>
              </div>
            )}


            {deploymentForm.type === 'dga' && (
              <div className="rounded p-4 mt-2 border transition-colors duration-200 bg-surface-secondary border-border-primary text-sm text-text-secondary">
                No additional configuration options
              </div>
            )}
            {/* Status Messages */}
            {deploymentSubmitStatus === 'error' && (
              <Alert variant="danger">
                <AlertDescription>
                  Failed to create deployment. Please check all fields and try
                  again.
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-border-primary/20">
              <Button
                variant="secondary"
                onClick={handleCloseAddDeploymentModal}
                disabled={deploymentSubmitStatus === 'submitting'}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleCreateDeployment}
                loading={deploymentSubmitStatus === 'submitting'}
                disabled={
                  deploymentSubmitStatus === 'submitting' ||
                  !deploymentForm.name.trim() ||
                  !deploymentForm.type
                }
              >
                {deploymentSubmitStatus === 'submitting'
                  ? 'Creating...'
                  : 'Add'}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}